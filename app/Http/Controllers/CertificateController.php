<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Font;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpWord\TemplateProcessor;

public function generateCertificate(Request $request)
{
    try {
        $data = $request->validate([
            'bank_name' => 'required|string|max:255',
            'cost_center' => 'required|string|max:50',
            'bank_address' => 'required|string|max:255',
            'application_number' => 'required|string|max:50',
            'county' => 'required|string|max:100',
            'legal_description' => 'required|string',
            'file_number' => 'required|string|max:50',
            'deed_date' => 'required|string|max:20',
            'filed_date' => 'required|string|max:20',
            'grantor' => 'required|string|max:255',
            'owner_name' => 'required|string|max:255',
            'property_address' => 'required|string|max:255',
            'liens_status' => 'required|string|max:100',
            'home_eq' => 'required|string|max:10',
            'parcel_number' => 'required|string|max:50',
            'assessed_value' => 'required|string|max:20',
            'property_tax' => 'required|string|max:20',
            'tax_status' => 'required|string|max:20',
            'index_date' => 'required|string|max:20',
            'invoice_amount' => 'required|string|max:20',
            'witness_date' => 'nullable|string|max:50',
        ]);

        $data['witness_date'] = $data['witness_date'] ?: now()->format('jS [day of] F Y');
        $template = storage_path('app/public/Abstract_Certificate_Template.dotx');

        if (!file_exists($template)) {
            Log::error('Template file not found: ' . $template);
            return response()->json(['error' => 'Template file not found'], 500);
        }

        $templateProcessor = new TemplateProcessor($template);
        $templateProcessor->setValue('BANK_NAME', htmlspecialchars($data['bank_name']));
        $templateProcessor->setValue('COST_CENTER', htmlspecialchars($data['cost_center']));
        $templateProcessor->setValue('BANK_ADDRESS', htmlspecialchars($data['bank_address']));
        $templateProcessor->setValue('APPLICATION_NUMBER', htmlspecialchars($data['application_number']));
        $templateProcessor->setValue('COUNTY', htmlspecialchars($data['county']));
        $templateProcessor->setValue('UPPER_COUNTY', htmlspecialchars(strtoupper($data['county'])));
        $templateProcessor->setValue('LEGAL_DESCRIPTION', htmlspecialchars(str_replace('{county}', $data['county'], $data['legal_description'])));
        $templateProcessor->setValue('FILE_NUMBER', htmlspecialchars($data['file_number']));
        $templateProcessor->setValue('DEED_DATE', htmlspecialchars($data['deed_date']));
        $templateProcessor->setValue('FILED_DATE', htmlspecialchars($data['filed_date']));
        $templateProcessor->setValue('GRANTOR', htmlspecialchars($data['grantor']));
        $templateProcessor->setValue('OWNER_NAME', htmlspecialchars($data['owner_name']));
        $templateProcessor->setValue('PROPERTY_ADDRESS', htmlspecialchars($data['property_address']));
        $templateProcessor->setValue('LIENS_STATUS', htmlspecialchars($data['liens_status']));
        $templateProcessor->setValue('HOME_EQ', htmlspecialchars($data['home_eq']));
        $templateProcessor->setValue('PARCEL_NUMBER', htmlspecialchars($data['parcel_number']));
        $templateProcessor->setValue('ASSESSED_VALUE', htmlspecialchars($data['assessed_value']));
        $templateProcessor->setValue('PROPERTY_TAX', htmlspecialchars($data['property_tax']));
        $templateProcessor->setValue('TAX_STATUS', htmlspecialchars($data['tax_status']));
        $templateProcessor->setValue('INDEX_DATE', htmlspecialchars($data['index_date']));
        $templateProcessor->setValue('INVOICE_AMOUNT', htmlspecialchars($data['invoice_amount']));
        $templateProcessor->setValue('WITNESS_DATE', htmlspecialchars($data['witness_date']));
        $templateProcessor->setValue('UPPER_OWNER_NAME', htmlspecialchars(strtoupper($data['owner_name'])));

        $filename = 'Abstract_Certificate_' . str_replace(' ', '_', $data['owner_name']) . '.docx';
        $tempFile = storage_path('app/public/' . $filename);
        $templateProcessor->saveAs($tempFile);

        if (!file_exists($tempFile) || filesize($tempFile) === 0) {
            Log::error('Document generation failed: File is empty or not created');
            return response()->json(['error' => 'Failed to generate document'], 500);
        }

        Log::info('Document generated successfully: ' . $filename . ', Size: ' . filesize($tempFile) . ' bytes');
        return Response::download($tempFile, $filename)->deleteFileAfterSend(true);
    } catch (\Exception $e) {
        Log::error('Error generating document: ' . $e->getMessage());
        return response()->json(['error' => 'Failed to generate document: ' . $e->getMessage()], 500);
    }
}
