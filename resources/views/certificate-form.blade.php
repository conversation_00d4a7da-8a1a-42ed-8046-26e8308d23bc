<!DOCTYPE html>
<html>
<head>
    <title>Abstractor's Certificate Generator</title>
    <link href="{{ asset('css/app.css') }}" rel="stylesheet">
    <style>
        .form-container { max-width: 800px; margin: 20px auto; }
        .form-group { margin-bottom: 15px; }
        label { display: inline-block; width: 200px; }
        input, textarea { width: 400px; padding: 5px; }
        textarea { height: 100px; }
        .submit-btn { margin-top: 20px; }
    </style>
</head>
<body>
<div class="form-container">
    <h2>Abstractor's Certificate Generator</h2>
    <form method="POST" action="{{ route('generate.certificate') }}">
        @csrf
        <div class="form-group">
            <label for="bank_name">Bank Name</label>
            <input type="text" name="bank_name" id="bank_name" required>
        </div>
        <div class="form-group">
            <label for="cost_center">Cost Center #</label>
            <input type="text" name="cost_center" id="cost_center" required>
        </div>
        <div class="form-group">
            <label for="bank_address">Bank Address</label>
            <input type="text" name="bank_address" id="bank_address" required>
        </div>
        <div class="form-group">
            <label for="application_number">Application Number</label>
            <input type="text" name="application_number" id="application_number" required>
        </div>
        <div class="form-group">
            <label for="county">County</label>
            <input type="text" name="county" id="county" required>
        </div>
        <div class="form-group">
            <label for="legal_description">Legal Description (use {county} for county)</label>
            <textarea name="legal_description" id="legal_description" required></textarea>
        </div>
        <div class="form-group">
            <label for="file_number">File Number</label>
            <input type="text" name="file_number" id="file_number" required>
        </div>
        <div class="form-group">
            <label for="deed_date">Deed Date (e.g., 1-28-22)</label>
            <input type="text" name="deed_date" id="deed_date" required>
        </div>
        <div class="form-group">
            <label for="filed_date">Filed Date (e.g., 2-1-22)</label>
            <input type="text" name="filed_date" id="filed_date" required>
        </div>
        <div class="form-group">
            <label for="grantor">Grantor</label>
            <input type="text" name="grantor" id="grantor" required>
        </div>
        <div class="form-group">
            <label for="owner_name">Owner Name</label>
            <input type="text" name="owner_name" id="owner_name" required>
        </div>
        <div class="form-group">
            <label for="property_address">Property Address</label>
            <input type="text" name="property_address" id="property_address" required>
        </div>
        <div class="form-group">
            <label for="liens_status">Liens Status (e.g., FREE AND CLEAR)</label>
            <input type="text" name="liens_status" id="liens_status" required>
        </div>
        <div class="form-group">
            <label for="home_eq">Home Equity (YES/NO)</label>
            <input type="text" name="home_eq" id="home_eq" required>
        </div>
        <div class="form-group">
            <label for="parcel_number">Parcel Number</label>
            <input type="text" name="parcel_number" id="parcel_number" required>
        </div>
        <div class="form-group">
            <label for="assessed_value">Assessed Value (e.g., $360,966.00)</label>
            <input type="text" name="assessed_value" id="assessed_value" required>
        </div>
        <div class="form-group">
            <label for="property_tax">Property Tax (e.g., $8,236.57)</label>
            <input type="text" name="property_tax" id="property_tax" required>
        </div>
        <div class="form-group">
            <label for="tax_status">Tax Status (e.g., PAID)</label>
            <input type="text" name="tax_status" id="tax_status" required>
        </div>
        <div class="form-group">
            <label for="index_date">Index Date (e.g., 5-19-2025)</label>
            <input type="text" name="index_date" id="index_date" required>
        </div>
        <div class="form-group">
            <label for="invoice_amount">Invoice Amount (e.g., 75.00)</label>
            <input type="text" name="invoice_amount" id="invoice_amount" required>
        </div>
        <div class="form-group">
            <label for="witness_date">Witness Date (leave blank for today)</label>
            <input type="text" name="witness_date" id="witness_date">
        </div>
        <button type="submit" class="submit-btn">Generate Document</button>
    </form>
</div>
</body>
</html>
